<tabs:PirateTab xmlns="https://spacestation14.io"
                 xmlns:tabs="clr-namespace:Content.Client.Options.UI.Tabs"
                 xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 x:Class="Content.Client.Options.UI.Tabs.PirateTab">
    <ScrollContainer>
        <BoxContainer Orientation="Vertical" Margin="2">
            <!-- Name Highlighting Section -->
            <Label Text="{Loc 'ui-options-pirate-name-highlighting'}"
                   StyleClasses="LabelHeading"
                   Margin="0 0 0 8"/>

            <cc:HSeparator/>

            <BoxContainer Orientation="Horizontal" Margin="2">
                <CheckBox Name="PingNameEnabledCheckBox"
                         Text="{Loc 'ui-options-pirate-ping-name-enabled'}"
                         ToolTip="{Loc 'ui-options-pirate-ping-name-enabled-tooltip'}"/>
            </BoxContainer>

            <BoxContainer Orientation="Horizontal" Margin="2">
                <CheckBox Name="PingNameSoundsCheckBox"
                         Text="{Loc 'ui-options-pirate-ping-name-sounds'}"
                         ToolTip="{Loc 'ui-options-pirate-ping-name-sounds-tooltip'}"/>
            </BoxContainer>

            <BoxContainer Orientation="Vertical" Margin="2">
                <Label Text="{Loc 'ui-options-pirate-ping-name-color'}"
                       Margin="0 0 0 4"/>

                <!-- Keep just the reset button -->
                <BoxContainer Orientation="Horizontal" Margin="0 4 0 4">
                    <Button Name="PingNameColorReset"
                           Text="{Loc 'ui-options-pirate-ping-name-color-reset'}"
                           ToolTip="{Loc 'ui-options-pirate-ping-name-color-reset-tooltip'}"/>
                </BoxContainer>

                <!-- RGB Sliders - keep this -->
                <BoxContainer Name="RgbContainer" Orientation="Vertical" Margin="0 4 0 4">
                    <ColorSelectorSliders Name="ColorSliders" HorizontalExpand="True"/>
                </BoxContainer>
                    <Button Name="PingNameColorPreview"
                           Text="■"
                           MinSize="30 30"
                           Margin="4 0 0 0"
                           ToolTip="{Loc 'ui-options-pirate-ping-name-color-preview-tooltip'}"/>
                </BoxContainer>
            </BoxContainer>


        </BoxContainer>
    </ScrollContainer>
</tabs:PirateTab>



