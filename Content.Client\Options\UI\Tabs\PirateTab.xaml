<tabs:PirateTab xmlns="https://spacestation14.io"
                 xmlns:tabs="clr-namespace:Content.Client.Options.UI.Tabs"
                 xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 x:Class="Content.Client.Options.UI.Tabs.PirateTab">
    <ScrollContainer>
        <BoxContainer Orientation="Vertical" Margin="2">
            <!-- Name Highlighting Section -->
            <Label Text="{Loc 'ui-options-pirate-name-highlighting'}"
                   StyleClasses="LabelHeading"
                   Margin="0 0 0 8"/>

            <cc:HSeparator/>

            <BoxContainer Orientation="Horizontal" Margin="2">
                <CheckBox Name="PingNameEnabledCheckBox"
                         Text="{Loc 'ui-options-pirate-ping-name-enabled'}"
                         ToolTip="{Loc 'ui-options-pirate-ping-name-enabled-tooltip'}"/>
            </BoxContainer>

            <BoxContainer Orientation="Horizontal" Margin="2">
                <CheckBox Name="PingNameSoundsCheckBox"
                         Text="{Loc 'ui-options-pirate-ping-name-sounds'}"
                         ToolTip="{Loc 'ui-options-pirate-ping-name-sounds-tooltip'}"/>
            </BoxContainer>

            <BoxContainer Orientation="Vertical" Margin="2">
                <Label Text="{Loc 'ui-options-pirate-ping-name-color'}"
                       Margin="0 0 0 4"/>

                <!-- Hex Color Input -->
                <BoxContainer Orientation="Horizontal" Margin="0 4 0 4">
                    <Label Text="{Loc 'ui-options-pirate-ping-name-color-hex'}"
                           MinSize="60 0"
                           Margin="0 0 4 0"/>
                    <LineEdit Name="PingNameColorLineEdit"
                             HorizontalExpand="True"
                             PlaceHolder="#FFFF00"
                             ToolTip="{Loc 'ui-options-pirate-ping-name-color-hex-tooltip'}"/>
                    <Button Name="PingNameColorReset"
                           Text="{Loc 'ui-options-pirate-ping-name-color-reset'}"
                           Margin="4 0 0 0"
                           ToolTip="{Loc 'ui-options-pirate-ping-name-color-reset-tooltip'}"/>
                </BoxContainer>

                <!-- RGB Sliders -->
                <BoxContainer Name="RgbContainer" Orientation="Vertical" Margin="0 4 0 4">
                    <ColorSelectorSliders Name="ColorSliders" HorizontalExpand="True"/>
                </BoxContainer>

                <!-- Color Preview -->
                <BoxContainer Orientation="Horizontal" Margin="0 4 0 4">
                    <Label Text="{Loc 'ui-options-pirate-ping-name-color-preview'}"
                           MinSize="60 0"
                           Margin="0 0 4 0"/>
                    <Button Name="PingNameColorPreview"
                           Text="■"
                           MinSize="30 30"
                           ToolTip="{Loc 'ui-options-pirate-ping-name-color-preview-tooltip'}"/>
                </BoxContainer>
            </BoxContainer>


        </BoxContainer>
    </ScrollContainer>
</tabs:PirateTab>



