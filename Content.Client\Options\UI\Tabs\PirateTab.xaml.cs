using System;
using Content.Shared._Pirate.CCVar;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Configuration;
using Robust.Shared.IoC;
using Robust.Shared.Maths;

namespace Content.Client.Options.UI.Tabs;

[GenerateTypedNameReferences]
public sealed partial class PirateTab : Control
{
    [Dependency] private readonly IConfigurationManager _cfg = default!;

    public PirateTab()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        PingNameEnabledCheckBox.OnToggled += OnPingNameEnabledToggled;
        PingNameSoundsCheckBox.OnToggled += OnPingNameSoundsToggled;

        // Color controls
        ColorSliders.OnColorChanged += OnColorSlidersChanged;
        PingNameColorReset.OnPressed += OnPingNameColorReset;

        UpdateValues();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            PingNameEnabledCheckBox.OnToggled -= OnPingNameEnabledToggled;
            PingNameSoundsCheckBox.OnToggled -= OnPingNameSoundsToggled;

            ColorSliders.OnColorChanged -= OnColorSlidersChanged;
            PingNameColorReset.OnPressed -= OnPingNameColorReset;
        }

        base.Dispose(disposing);
    }

    private void UpdateValues()
    {
        PingNameEnabledCheckBox.Pressed = _cfg.GetCVar(PirateCVars.PingNameEnabled);
        PingNameSoundsCheckBox.Pressed = _cfg.GetCVar(PirateCVars.PingNameSoundsEnabled);

        // Update color sliders from saved color
        var colorHex = _cfg.GetCVar(PirateCVars.PingNameColor);
        var color = Color.TryFromHex(colorHex) ?? Color.Yellow;
        ColorSliders.Color = color;

        UpdateColorPreview();
    }

    private void UpdateColorPreview()
    {
        var colorHex = _cfg.GetCVar(PirateCVars.PingNameColor);
        var color = Color.TryFromHex(colorHex);
        if (color.HasValue)
        {
            PingNameColorPreview.Modulate = color.Value;
        }
        else
        {
            PingNameColorPreview.Modulate = Color.Gray;
        }
    }

    private void OnPingNameEnabledToggled(BaseButton.ButtonToggledEventArgs args)
    {
        _cfg.SetCVar(PirateCVars.PingNameEnabled, args.Pressed);
    }

    private void OnPingNameSoundsToggled(BaseButton.ButtonToggledEventArgs args)
    {
        _cfg.SetCVar(PirateCVars.PingNameSoundsEnabled, args.Pressed);
    }

    private void OnColorSlidersChanged(Color color)
    {
        var colorHex = color.ToHex();
        _cfg.SetCVar(PirateCVars.PingNameColor, colorHex);
        UpdateColorPreview();
    }

    private void OnPingNameColorReset(BaseButton.ButtonEventArgs args)
    {
        var defaultColor = "#FFFF00"; // Yellow
        _cfg.SetCVar(PirateCVars.PingNameColor, defaultColor);

        // Update sliders to show the default color
        var color = Color.TryFromHex(defaultColor) ?? Color.Yellow;
        ColorSliders.Color = color;

        UpdateColorPreview();
    }


}




