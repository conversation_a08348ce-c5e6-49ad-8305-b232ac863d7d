using System.Text.RegularExpressions;
using Content.Shared._Pirate.CCVar;
using Content.Shared._Pirate.PingName;
using Content.Shared.Chat;
using Robust.Client.Player;
using Robust.Shared.Configuration;
using Robust.Shared.GameObjects;
using Robust.Shared.Log;
using Robust.Shared.Maths;
using Robust.Shared.Player;

namespace Content.Client._Pirate.PingName;

public sealed class PingNameSystem : SharedPingNameSystem
{
    [Dependency] private readonly IPlayerManager _player = default!;
    [Dependency] private readonly IConfigurationManager _cfg = default!;

    private ISawmill _sawmill = default!;
    private List<string> _playerNameRoots = new();

    public override void Initialize()
    {
        base.Initialize();
        _sawmill = Logger.GetSawmill("pirate.ping_name");

        // Subscribe to player attachment events to get the player's name
        _player.LocalPlayerAttached += OnLocalPlayerAttached;

        // Subscribe to metadata changes to catch name changes
        SubscribeLocalEvent<MetaDataComponent, ComponentInit>(OnMetaDataInit);
        SubscribeLocalEvent<MetaDataComponent, EntityRenamedEvent>(OnEntityRenamed);
    }

    private void OnLocalPlayerAttached(LocalPlayerAttachedEvent ev)
    {
        _sawmill.Debug($"Local player attached: {ev.Entity}");

        // Get the player's name and update our name roots
        if (TryComp<MetaDataComponent>(ev.Entity, out var meta))
        {
            var playerName = meta.EntityName;
            _sawmill.Debug($"Player name: '{playerName}'");
            UpdatePlayerNameRoots(playerName);
        }
        else
        {
            _sawmill.Debug($"No MetaDataComponent found for entity: {ev.Entity}");
        }
    }

    private void UpdatePlayerNameRoots(string fullName)
    {
        _playerNameRoots.Clear();

        if (string.IsNullOrWhiteSpace(fullName))
        {
            _sawmill.Debug("Full name is null or whitespace");
            return;
        }

        var nameParts = fullName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        _sawmill.Debug($"Split name into {nameParts.Length} parts: [{string.Join(", ", nameParts)}]");

        foreach (var part in nameParts)
        {
            var nameRoot = GetNameRoot(part);
            _sawmill.Debug($"Part: '{part}' -> Root: '{nameRoot}' (length: {nameRoot.Length})");

            if (nameRoot.Length >= 3) // не хочемо підсвічувати занадто короткі корені
            {
                _playerNameRoots.Add(nameRoot);
                _sawmill.Debug($"Added root '{nameRoot}'");
            }
            else
            {
                _sawmill.Debug($"Skipped part '{part}' - root too short");
            }
        }

        _sawmill.Debug($"Final name roots: [{string.Join(", ", _playerNameRoots)}]");
    }

    private void OnMetaDataInit(EntityUid uid, MetaDataComponent component, ComponentInit args)
    {
        // Check if this is our local player
        if (_player.LocalEntity == uid)
        {
            _sawmill.Debug($"MetaData init for local player: {uid}, name: '{component.EntityName}'");
            UpdatePlayerNameRoots(component.EntityName);
        }
    }

    private void OnEntityRenamed(EntityUid uid, MetaDataComponent component, EntityRenamedEvent args)
    {
        // Check if this is our local player
        if (_player.LocalEntity == uid)
        {
            _sawmill.Debug($"Local player renamed: {uid}, old: '{args.OldName}', new: '{args.NewName}'");
            UpdatePlayerNameRoots(args.NewName);
        }
    }

    /// <summary>
    /// Highlights names in a chat message based on the player's ping name component.
    /// </summary>
    public string HighlightNamesInMessage(string message)
    {
        _sawmill.Debug($"HighlightNamesInMessage called with: '{message}'");

        // Check if name highlighting is enabled
        if (!_cfg.GetCVar(PirateCVars.PingNameEnabled))
        {
            _sawmill.Debug("Name highlighting disabled in CVars");
            return message;
        }

        // Check if we have any name roots to highlight
        if (_playerNameRoots.Count == 0)
        {
            _sawmill.Debug("No player name roots available");
            return message;
        }

        var result = message;

        // Get color from CVars and parse it
        var colorHex = _cfg.GetCVar(PirateCVars.PingNameColor);
        var color = Color.TryFromHex(colorHex) ?? Color.Yellow;
        var font = "bold";

        _sawmill.Debug($"Found {_playerNameRoots.Count} name roots: [{string.Join(", ", _playerNameRoots)}]");
        _sawmill.Debug($"Using color: {colorHex} -> {color}");

        foreach (var nameRoot in _playerNameRoots)
        {
            _sawmill.Debug($"Processing name root: '{nameRoot}'");
            var oldResult = result;
            result = HighlightNameInMessage(result, nameRoot, color, font);
            if (oldResult != result)
            {
                _sawmill.Debug($"Name root '{nameRoot}' matched and highlighted");
            }
            else
            {
                _sawmill.Debug($"Name root '{nameRoot}' did not match");
            }
        }

        _sawmill.Debug($"Final result: '{result}'");
        return result;
    }

    /// <summary>
    /// Highlights a specific name root in a message.
    /// </summary>
    private string HighlightNameInMessage(string message, string nameRoot, Color color, string? font = null)
    {
        var colorHex = color.ToHex();

        // Create pattern to match words that start with the name root
        var escapedRoot = EscapeRegexSpecialChars(nameRoot);
        var namePattern = $@"\b({escapedRoot}\w*)\b";

        // Use similar approach to codewords but with regex
        string replacement;
        if (font != null)
        {
            replacement = $"[font={font}][color={colorHex}]$1[/color][/font]";
        }
        else
        {
            replacement = $"[color={colorHex}]$1[/color]";
        }

        // For radio messages and other complex messages, we need a different approach
        // Check if this is a BubbleContent message (speech bubbles)
        var bubbleContentPattern = @"(\[BubbleContent\])(.*?)(\[/BubbleContent\])";
        if (Regex.IsMatch(message, bubbleContentPattern))
        {
            // Only highlight names in BubbleContent, not in BubbleHeader (sender name)
            var result = Regex.Replace(message, bubbleContentPattern, match =>
            {
                var openTag = match.Groups[1].Value;
                var content = match.Groups[2].Value;
                var closeTag = match.Groups[3].Value;

                // Apply name highlighting only within the content, ignoring existing tags
                var highlightedContent = Regex.Replace(content, namePattern, replacement, RegexOptions.IgnoreCase);

                return openTag + highlightedContent + closeTag;
            }, RegexOptions.Singleline);

            return result;
        }
        else
        {
            // For radio and other messages without BubbleContent, apply highlighting everywhere
            // but avoid highlighting inside the sender name part (before the verb)
            // Radio format: [color=...][channel] [name] verb, [message]

            // Try to find the message part after the verb and comma
            var radioPattern = @"(.*?[,:][\s]*[""]*)(.*?)([""]*[\s]*\[/.*?\]*)$";
            var radioMatch = Regex.Match(message, radioPattern, RegexOptions.Singleline);

            if (radioMatch.Success)
            {
                var prefix = radioMatch.Groups[1].Value;
                var messageContent = radioMatch.Groups[2].Value;
                var suffix = radioMatch.Groups[3].Value;

                // Apply highlighting only to the message content
                var highlightedContent = Regex.Replace(messageContent, namePattern, replacement, RegexOptions.IgnoreCase);

                return prefix + highlightedContent + suffix;
            }
            else
            {
                // Fallback: apply highlighting to the whole message
                return Regex.Replace(message, namePattern, replacement, RegexOptions.IgnoreCase);
            }
        }
    }

    /// <summary>
    /// Escapes special regex characters in a string.
    /// </summary>
    private string EscapeRegexSpecialChars(string input)
    {
        return input.Replace("\\", "\\\\")
                   .Replace(".", "\\.")
                   .Replace("+", "\\+")
                   .Replace("*", "\\*")
                   .Replace("?", "\\?")
                   .Replace("^", "\\^")
                   .Replace("$", "\\$")
                   .Replace("(", "\\(")
                   .Replace(")", "\\)")
                   .Replace("[", "\\[")
                   .Replace("]", "\\]")
                   .Replace("{", "\\}")
                   .Replace("}", "\\}")
                   .Replace("|", "\\|");
    }
}
